SELECT TOP (1000) [ConstructionId]
      ,[Deleted]
      ,[CreatedOn]
      ,[CreatedByName]
      ,[ModifiedOn]
      ,[ModifiedByName]
      ,[Description]
      ,[DisplayDescription]
      ,[ManufacturerId]
      ,[ExternalConstructionId]
      ,[AdjacencyCode]
      ,[UnitOfMeasureCode]
      ,[Comments]
      ,[LifeCycleDataJson]
      ,[ChenathDataJson]
      ,[FR5DataJson]
      ,[HeroDataJson]
      ,[EPDataJson]
      ,[ConstructionCategoryCode]
      ,[AirCavityCode]
      ,[Thickness]
      ,[Density]
      ,[FloorCovering]
      ,[Tilt]
      ,[ExteriorSolarAbsorptance]
      ,[InteriorSolarAbsorptance]
      ,[SystemRValue]
      ,[OpeningStyleCode]
      ,[Openability]
      ,[HasWeatherStrip]
      ,[HasInsectScreen]
      ,[InsulationDataJson]
      ,[VisualisationDataJson]
      ,[AllowEditingExteriorSolarAbsorptance]
      ,[AllowEditingInteriorSolarAbsorptance]
      ,[ExteriorColourId]
      ,[InteriorColourId]
      ,[AllowEditingExteriorColour]
      ,[AllowEditingInteriorColour]
      ,[ThermalBridge]
      ,[AutomaticOpenabilityCalculation]
      ,[NccOpeningStyleCode]
      ,[Azimuth]
      ,[ShadeProjection]
      ,[ShadeOffset]
      ,[GrossArea]
      ,[NetArea]
      ,[Height]
      ,[Width]
      ,[AllowEditingOpeningStyle]
      ,[IsFullMasonry]
      ,[ShowInReport]
      ,[ConstructionSubCategoryCode]
      ,[IsFavourite]
  FROM [dbo].[RSS_SurfaceTemplate]
  WHERE [Description] LIKE '%Cavity masonry%'
    -- AND [Deleted] = 1
AND [ConstructionId] = '283f53ab-b557-45db-b434-04171a9a0242'


UPDATE [dbo].[RSS_SurfaceTemplate] SET [Deleted] = 0 WHERE [ConstructionId] = '283f53ab-b557-45db-b434-04171a9a0242'