<style>

    .autocomplete-different > md-autocomplete-wrap > md-input-container > input {
        color: orange !important;
    }

    .input-different > input,
    .input-different > div > div > input {
        color: orange !important;
    }

    .checkbox-different {
        color: orange !important;
    }

    .select-different,
    .select-different > md-select-value,
    .select-different > md-select-value > span {
        color: orange !important;
    }

    .radio-different,
    .radio-different > md-radio-button,
    .radio-different > md-radio-button > div,
    .radio-different > md-radio-button > div > span {
        color: orange !important;
    }
</style>

<!-- TODO: Optimize display. Seems to be a performance problem. -->
<div ng-form="BuildingElementsOptionForm{{vm.option.optionIndex}}{{vm.buildingType}}"
     style="overflow-x: scroll; ">

    <!-- Template Base + Selection + Clear -->
    <md-card layout-margin ng-if="vm.buildingType != 'template'">

        <md-card-header class="layout-column">

            <building-construction-template-selector type="vm.generalSectionDisplay"
                                                     building="vm.building"
                                                     building-type="vm.buildingType"
                                                     option="vm.option"
                                                     disabled="vm.disabled"
                                                     new-job="false"
                                                     show-copy-previous="false">
            </building-construction-template-selector>
            <div ng-if="!vm.isTemplate"
                 layout="row"
                 class="md-block"
                 style="margin-left: auto">
                <md-button ng-click="vm.copyBuildingElementsToFrom(option, option[vm.buildingType], vm.baselineOption[vm.buildingType], vm.baselineOption)"
                           class="md-raised"
                           ng-disabled="!vm.enableCopyToFrom(option[vm.buildingType], vm.baselineOption[vm.buildingType]);"
                           ng-show="!vm.disabledEx() && vm.showCopyBaselineForOption(vm.baselineOption) && vm.comparisonBuilding != null">
                    Copy Baseline
                </md-button>

                <md-button ng-click="vm.copyBuildingElementsToFrom(option, option.reference, option.proposed)"
                           class="md-raised"
                           ng-disabled="!vm.enableCopyToFrom(option.reference, option.proposed);"
                           ng-show="!vm.disabledEx() && vm.buildingType=='reference'">
                    Copy Proposed
                </md-button>

                <!-- Now for every compliance option EXCEPT THIS ONE AND THE BASELINE show a "copy Option X" button -->
                <md-button ng-repeat="opt in vm.optionsNotThisOrBaseline(vm.option)"
                           ng-if="!vm.disabledEx() && vm.option.optionIndex != 0 && vm.showCopyBaselineForOption(opt)"
                           ng-disabled="!vm.enableCopyToFrom(option[vm.buildingType], opt[vm.buildingType]);"
                           ng-click="vm.copyBuildingElementsToFrom(option, option[vm.buildingType], opt[vm.buildingType], opt)">
                    Copy Option {{opt.optionIndex}}
                </md-button>
            </div>

        </md-card-header>
    </md-card>

    <!-- Loop over all desired categories IN ORDER SET BY CALLING CODE (CONFIRM ???) -->
    <md-card ng-repeat="category in vm.constructionCategoryList track by category.constructionCategoryCode"
             layout-margin
             ng-form="{{category.constructionCategoryCode}}ConstructionForm{{option.optionIndex}}">
        <md-card-header layout="row" layout-wrap>
            <span class="md-title clickable"
                  layout="row"
                  ng-click="vm.expandSection(category.constructionCategoryCode)"
                  ng-class="{'card-has-errors' : {{category.constructionCategoryCode}}ConstructionForm{{option.optionIndex}}.$invalid==true ||
                                                  ((building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()] == false || building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()] == null) && !vm.hasConstructionItems(category, building)) &&
                                                  !vm.externalDataIsSpecified(category, vm.building) }">

                <span style="margin-right: 15px;"
                      ng-style="{color: building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()] ? 'lightgrey' : 'inherit'}">
                    {{category.title}}
                </span>
                <span style="margin-right: 15px; display: inline-block;"
                      ng-if="!building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()]">
                    <i ng-if="vm.sectionExpansions[category.constructionCategoryCode]"
                       class="fa fa-caret-up" />
                    <i ng-if="(!vm.sectionExpansions[category.constructionCategoryCode])"
                       class="fa fa-caret-down" />
                </span>

                <!-- For certain Categories we have the option that say that external data has been supplied for them, so there is no need for it here. -->
                <md-radio-group ng-if="!vm.disabledEx() &&
                                       ((building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()] == false ||
                                         building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()] == null ||
                                         building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()] == undefined) &&
                                        category.allowExternalData == true)"
                                ng-model="vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()]"
                                layout="row"
                                style="margin-left: 5px; transform: scale(0.65); color: #333333;">
                    <md-radio-button ng-value="false">
                        Specified&nbsp;
                    </md-radio-button>
                    <md-radio-button ng-value="true"
                                     ng-click="vm.clearCategory(category, building)">
                        {{category.externalDataSourceTitle}} &nbsp;
                    </md-radio-button>

                </md-radio-group>

            </span>
            <span flex></span>

            <!-- Export to Exterior Glazing Button (Exterior Glazing only) -->
            <div redi-show-by-roles="['assessment_actions__glazingcalculator']"
                 ng-if="category.constructionCategoryCode == 'ExteriorGlazing'"
                 class="nav-image-div clickable"
                 style="margin: auto; margin-right: 20px;"
                 ng-click="vm.launchGlazingCalcCallback(vm.assessment, vm.option, vm.building)">
                <i class="fa fa-calculator fa-2x"></i>
                <md-tooltip md-direction="top">
                    Export to Glazing Calculator
                </md-tooltip>
            </div>

            <md-checkbox name="{{category.title}}Required"
                         style="margin: auto;"
                         ng-model="building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()]"
                         ng-change="vm.clearCategory(category, building)"
                         ng-disabled="vm.disabledEx()">
                Not Applicable
            </md-checkbox>
        </md-card-header>
        <md-card-content ng-if="building.categoriesNotRequired[category.constructionCategoryCode.toLowerCase()] != true">

            <!-- If this is a glazing category and we DON'T want to manually specify the glazing, include this note. -->
            <div ng-if="vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == true"
                 style="padding: 20px; margin: 10px; background-color: rgba(255, 255, 255, 0.8); z-index: 777;
                        display: flex; flex-direction: column; justify-content: center; align-items: center;">
                <h1>Refer to {{category.externalDataSourceTitle}}</h1>
            </div>

            <!-----------------------------
                CONSTRUCTION TAB
            ------------------------------>

            <!-- Otherwise, loop over all our grouped headings + expandable buttons-->
            <!-- ROOF CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('Roof') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'Roof' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%" ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Exterior Solar Absorptance</span>
                        <span class="header">Roof Space Ventilation</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-exterior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'exteriorSolarAbsorptance', 'exteriorColour');"/>
                        <div ng-include="'construction-parent-ventilation-input'" />
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                            style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('tilt', parent);"
                                        ng-click="vm.sortBy('tilt', parent);"
                                        class="clickable">
                                        Roof Pitch ({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-click="vm.sortBy('azimuth', parent);"
                                        class="clickable">
                                        Azimuth ({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-click="vm.sortBy('sector', parent);"
                                        class="clickable">
                                        Sector
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Gross Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-click="vm.sortBy('netArea', parent);"
                                        class="clickable">
                                        Net Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                             ng-click="vm.openVisibilityModal(parent)">
                                                <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);" ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);" ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);" ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('tilt', parent);" ng-include="'construction-element-tilt-input'" />
                                    <td ng-if="vm.isTableColumnVisible('azimuth', parent);" ng-include="'construction-element-azimuth-input'" />
                                    <td ng-if="vm.isTableColumnVisible('sector', parent);" ng-include="'construction-element-sector-input'" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);" ng-include="'construction-element-gross-area-input'" />
                                    <td ng-if="vm.isTableColumnVisible('netArea', parent);" ng-include="'construction-element-net-area-input'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>

                </div>

            </div>

            <!-- CEILING (ROOF / ROOF SPACE ABOVE) CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('CeilingRoofAbove') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'CeilingRoofAbove' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <div class="construction-parent-header">
                <!-- PARENT HEADER/TABLE-->

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">

                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <!-- ELEMENTS TABLE -->
                        <table class="elements-table">
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                        style="min-width: 180px;"
                                        class="clickable">
                                        Adjacent Zone
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('tilt', parent);"
                                        ng-click="vm.sortBy('tilt', parent);"
                                        class="clickable">
                                        Ceiling Pitch ({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Gross Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-click="vm.sortBy('netArea', parent);"
                                        class="clickable">
                                        Net Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                            <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                               <img src="/content/feather/edit.svg" />
                                       </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);" ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);" ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);" ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);" ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('tilt', parent);" ng-include="'construction-element-tilt-input-optional'" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);" ng-include="'construction-element-gross-area-input'" />
                                    <td ng-if="vm.isTableColumnVisible('netArea', parent);" ng-include="'construction-element-net-area-input'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>
            </div>

            <!-- CEILING (NEIGHBOUR ABOVE) CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('CeilingNeighbourAbove') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'CeilingNeighbourAbove' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">

                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <!-- ELEMENTS TABLE -->
                        <table class="elements-table">
                            <thead>
                            <tr>
                                <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-click="vm.sortBy('elementNumber', parent);"
                                    class="clickable">
                                    Element
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Parent Zone
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                    ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                    style="min-width: 180px;"
                                    class="clickable">
                                    Adjacent Zone
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-click="vm.sortBy('storey', parent);"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Storey
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('tilt', parent);"
                                    ng-click="vm.sortBy('tilt', parent);"
                                    class="clickable">
                                    Ceiling Pitch ({{vm.symbol("degrees")}})
                                    <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-click="vm.sortBy('grossArea', parent);"
                                    class="clickable">
                                    Gross Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-click="vm.sortBy('netArea', parent);"
                                    class="clickable">
                                    Net Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);" ng-include="'construction-element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);" ng-include="'construction-element-parent-zone-input'" />
                                <td>Neighbour</td>
                                <td ng-if="vm.isTableColumnVisible('storey', parent);" ng-include="'construction-element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('tilt', parent);" ng-include="'construction-element-tilt-input-optional'" />
                                <td ng-if="vm.isTableColumnVisible('grossArea', parent);" ng-include="'construction-element-gross-area-input'" />
                                <td ng-if="vm.isTableColumnVisible('netArea', parent);" ng-include="'construction-element-net-area-input'" />
                                <td ng-include="'construction-element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>
            </div>

            <!-- EXTERIOR WALL CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('ExteriorWall') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'ExteriorWall' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Exterior Solar Absorptance</span>
                        <span class="header">Width (m)</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-exterior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'exteriorSolarAbsorptance', 'exteriorColour');"/>
                        <div ng-include="'construction-parent-width-calculation'" />
                        <div ng-include="'construction-parent-gross-area-calculation'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">

                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <!-- ELEMENTS TABLE -->
                        <table class="elements-table">
                            <thead>

                                <!-- Grouped Headers (Make dynamic somehow) -->
                                <tr>
                                    <th class="text-left"
                                        style="border-bottom: none;"
                                        colspan="{{vm.getShadowHeaderStartPosition(parent)}}" />
                                    <th style="text-align: center"
                                        colspan="{{vm.getShadowHeaderWidth(parent)}}">
                                        Shading
                                    </th>
                                </tr>

                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-click="vm.sortBy('azimuth', parent);"
                                        class="clickable">
                                        Azimuth ({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-click="vm.sortBy('sector', parent);"
                                        class="clickable">
                                        Sector
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-click="vm.sortBy('height', parent);"
                                        class="clickable">
                                        Height (m)
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-click="vm.sortBy('width', parent);"
                                        class="clickable">
                                        Width (m)
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('horizontalShading', parent);"
                                        ng-click="vm.sortHorizontalShading(parent)"
                                        class="clickable">
                                        Horizontal
                                        <i ng-if="parent.sortInfo.column === 'horizontalShading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'horizontalShading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('verticalShading', parent);"
                                        ng-click="vm.sortVerticalShading(parent)"
                                        class="clickable">
                                        Vertical
                                        <i ng-if="parent.sortInfo.column === 'verticalShading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'verticalShading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('leftWingWall.shading', parent);"
                                        ng-click="vm.sortBy('leftWingWall.shading', parent, vm.wallOutcomeTransform);"
                                        class="clickable">
                                        Left Wing
                                        <i ng-if="parent.sortInfo.column === 'leftWingWall.shading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'leftWingWall.shading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('rightWingWall.shading', parent);"
                                        ng-click="vm.sortBy('rightWingWall.shading', parent, vm.wallOutcomeTransform);"
                                        class="clickable">
                                        Right Wing
                                        <i ng-if="parent.sortInfo.column === 'rightWingWall.shading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'rightWingWall.shading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Gross Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-click="vm.sortBy('netArea', parent);"
                                        class="clickable">
                                        Net Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-include="'construction-element-azimuth-input'" />
                                    <td ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-include="'construction-element-sector-input'" />
                                    <td ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-include="'construction-element-height-input'" />
                                    <td ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-include="'construction-element-width-input'" />
                                    <td ng-if="vm.isTableColumnVisible('horizontalShading', parent);"
                                        ng-include="'construction-element-shade-horizontal-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'horizontalShading')" />
                                    <td ng-if="vm.isTableColumnVisible('verticalShading', parent);"
                                        ng-include="'construction-element-shade-vertical-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'verticalShading')" />
                                    <td ng-if="vm.isTableColumnVisible('leftWingWall.shading', parent);"
                                        ng-include="'construction-element-shade-left-wing-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'leftWingWall')" />
                                    <td ng-if="vm.isTableColumnVisible('rightWingWall.shading', parent);"
                                        ng-include="'construction-element-shade-right-wing-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'rightWingWall')" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'construction-element-gross-area-calculation'" />
                                    <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-include="'construction-element-net-area-input'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>
            </div>

            <!-- SUBFLOOR WALL CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('SubfloorWall') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'SubfloorWall' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Exterior Solar Absorptance</span>
                        <span class="header">Width (m)</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-exterior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'exteriorSolarAbsorptance', 'exteriorColour');"/>
                        <div ng-include="'construction-parent-width-calculation'" />
                        <div ng-include="'construction-parent-gross-area-calculation'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">

                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <!-- ELEMENTS TABLE -->
                        <table class="elements-table">
                            <thead>
                            <tr>
                                <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-click="vm.sortBy('elementNumber', parent);"
                                    class="clickable">
                                    Element
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Parent Zone
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-click="vm.sortBy('storey', parent);"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Storey Above
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                    ng-click="vm.sortBy('azimuth', parent);"
                                    class="clickable">
                                    Azimuth ({{vm.symbol("degrees")}})
                                    <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('sector', parent);"
                                    ng-click="vm.sortBy('sector', parent);"
                                    class="clickable">
                                    Sector
                                    <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('height', parent);"
                                    ng-click="vm.sortBy('height', parent);"
                                    class="clickable">
                                    Height (m)
                                    <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('width', parent);"
                                    ng-click="vm.sortBy('width', parent);"
                                    class="clickable">
                                    Width (m)
                                    <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-click="vm.sortBy('grossArea', parent);"
                                    class="clickable">
                                    Gross Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-click="vm.sortBy('netArea', parent);"
                                    class="clickable">
                                    Net Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-include="'construction-element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-include="'construction-element-parent-zone-subfloor-only-input'" />
                                <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-include="'construction-element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                    ng-include="'construction-element-azimuth-input'" />
                                <td ng-if="vm.isTableColumnVisible('sector', parent);"
                                    ng-include="'construction-element-sector-input'" />
                                <td ng-if="vm.isTableColumnVisible('height', parent);"
                                    ng-include="'construction-element-height-input'" />
                                <td ng-if="vm.isTableColumnVisible('width', parent);"
                                    ng-include="'construction-element-width-input'" />
                                <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-include="'construction-element-gross-area-calculation'" />
                                <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-include="'construction-element-net-area-input'" />
                                <td ng-include="'construction-element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>
            </div>

            <!-- INTERIOR WALL CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('InteriorWall') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'InteriorWall' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Width (m)</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-width-calculation'" />
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">

                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <!-- ELEMENTS TABLE -->
                        <table class="elements-table">
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                        style="min-width: 180px;"
                                        class="clickable">
                                        Adjacent Zone
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-click="vm.sortBy('height', parent);"
                                        class="clickable">
                                        Height (m)
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-click="vm.sortBy('width', parent);"
                                        class="clickable">
                                        Width (m)
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Gross Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-click="vm.sortBy('netArea', parent);"
                                        class="clickable">
                                        Net Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-include="'construction-element-height-input-optional'" />
                                    <td ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-include="'construction-element-width-input-optional'" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'construction-element-gross-area-input-fixed'" />
                                    <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-include="'construction-element-net-area-input'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>
            </div>

            <!-- INTERIOR WALL (ADJACENT TO ROOF SPACE) CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('InteriorWallAdjacentToRoofSpace') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'InteriorWallAdjacentToRoofSpace' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Width (m)</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-width-calculation'" />
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">

                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <!-- ELEMENTS TABLE -->
                        <table class="elements-table">
                            <thead>
                            <tr>
                                <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-click="vm.sortBy('elementNumber', parent);"
                                    class="clickable">
                                    Element
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Parent Zone
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                    ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                    style="min-width: 180px;"
                                    class="clickable">
                                    Adjacent Zone
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-click="vm.sortBy('storey', parent);"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Storey
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('height', parent);"
                                    ng-click="vm.sortBy('height', parent);"
                                    class="clickable">
                                    Height (m)
                                    <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('width', parent);"
                                    ng-click="vm.sortBy('width', parent);"
                                    class="clickable">
                                    Width (m)
                                    <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-click="vm.sortBy('grossArea', parent);"
                                    class="clickable">
                                    Gross Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-click="vm.sortBy('netArea', parent);"
                                    class="clickable">
                                    Net Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-include="'construction-element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-include="'construction-element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                    ng-include="'construction-element-adjacent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-include="'construction-element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('height', parent);"
                                    ng-include="'construction-element-height-input-optional'" />
                                <td ng-if="vm.isTableColumnVisible('width', parent);"
                                    ng-include="'construction-element-width-input-optional'" />
                                <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-include="'construction-element-gross-area-input-fixed'" />
                                <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-include="'construction-element-net-area-input'" />
                                <td ng-include="'construction-element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>
            </div>

            <!-- INTERIOR WALL (ADJACENT TO SUBFLOOR SPACE) CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('InteriorWallAdjacentToSubfloorSpace') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'InteriorWallAdjacentToSubfloorSpace' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Width (m)</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-width-calculation'" />
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">

                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <!-- ELEMENTS TABLE -->
                        <table class="elements-table">
                            <thead>
                            <tr>
                                <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-click="vm.sortBy('elementNumber', parent);"
                                    class="clickable">
                                    Element
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Parent Zone
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                    ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                    style="min-width: 180px;"
                                    class="clickable">
                                    Adjacent Zone
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-click="vm.sortBy('storey', parent);"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Storey
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('height', parent);"
                                    ng-click="vm.sortBy('height', parent);"
                                    class="clickable">
                                    Height (m)
                                    <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('width', parent);"
                                    ng-click="vm.sortBy('width', parent);"
                                    class="clickable">
                                    Width (m)
                                    <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-click="vm.sortBy('grossArea', parent);"
                                    class="clickable">
                                    Gross Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-click="vm.sortBy('netArea', parent);"
                                    class="clickable">
                                    Net Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-include="'construction-element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-include="'construction-element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                    ng-include="'construction-element-adjacent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-include="'construction-element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('height', parent);"
                                    ng-include="'construction-element-height-input-optional'" />
                                <td ng-if="vm.isTableColumnVisible('width', parent);"
                                    ng-include="'construction-element-width-input-optional'" />
                                <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-include="'construction-element-gross-area-input-fixed'" />
                                <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-include="'construction-element-net-area-input'" />
                                <td ng-include="'construction-element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>
            </div>

            <!-- INTERIOR WALL (ADJACENT TO NEIGHBOUR) CATEGORY -->
            <div ng-repeat="parent in vm.surfacesInCategory('InteriorWallAdjacentToNeighbour') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'InteriorWallAdjacentToNeighbour' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Width (m)</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-width-calculation'" />
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">

                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <!-- ELEMENTS TABLE -->
                        <table class="elements-table">
                            <thead>
                            <tr>
                                <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-click="vm.sortBy('elementNumber', parent);"
                                    class="clickable">
                                    Element
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Parent Zone
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                    ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                    style="min-width: 180px;"
                                    class="clickable">
                                    Adjacent Zone
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-click="vm.sortBy('storey', parent);"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Storey
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('height', parent);"
                                    ng-click="vm.sortBy('height', parent);"
                                    class="clickable">
                                    Height (m)
                                    <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('width', parent);"
                                    ng-click="vm.sortBy('width', parent);"
                                    class="clickable">
                                    Width (m)
                                    <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-click="vm.sortBy('grossArea', parent);"
                                    class="clickable">
                                    Gross Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-click="vm.sortBy('netArea', parent);"
                                    class="clickable">
                                    Net Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-include="'construction-element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-include="'construction-element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);">
                                    Neighbour
                                </td>
                                <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-include="'construction-element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('height', parent);"
                                    ng-include="'construction-element-height-input-optional'" />
                                <td ng-if="vm.isTableColumnVisible('width', parent);"
                                    ng-include="'construction-element-width-input-optional'" />
                                <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-include="'construction-element-gross-area-input-fixed'" />
                                <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-include="'construction-element-net-area-input'" />
                                <td ng-include="'construction-element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>
            </div>

            <!-- EXTERIOR FLOOR (CONNECTED TO GROUND) -->
            <div ng-repeat="parent in vm.surfacesInCategory('GroundFloor') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'GroundFloor' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Subfloor Ventilation</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-ventilation-input'" />
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);">
                                        Adjacent Zone
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Gross Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-click="vm.sortBy('netArea', parent);"
                                        class="clickable">
                                        Net Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'construction-element-gross-area-input'" />
                                    <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-include="'construction-element-net-area-input'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- EXTERIOR FLOOR (SUSPENDED) -->
            <div ng-repeat="parent in vm.surfacesInCategory('ExteriorFloor') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'ExteriorFloor' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Subfloor Ventilation</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-ventilation-input'" />
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                        style="min-width: 180px;"
                                        class="clickable">
                                        Adjacent Zone
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Gross Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-click="vm.sortBy('netArea', parent);"
                                        class="clickable">
                                        Net Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'construction-element-gross-area-input'" />
                                    <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-include="'construction-element-net-area-input'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- EXTERIOR FLOOR (ELEVATED) -->
            <div ng-repeat="parent in vm.surfacesInCategory('ExteriorFloorElevated') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'ExteriorFloorElevated' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Subfloor Ventilation</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-ventilation-input'" />
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                            <tr>
                                <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-click="vm.sortBy('elementNumber', parent);"
                                    class="clickable">
                                    Element
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Parent Zone
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-click="vm.sortBy('storey', parent);"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Storey
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-click="vm.sortBy('grossArea', parent);"
                                    class="clickable">
                                    Gross Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-click="vm.sortBy('netArea', parent);"
                                    class="clickable">
                                    Net Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-include="'construction-element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-include="'construction-element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-include="'construction-element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-include="'construction-element-gross-area-input'" />
                                <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-include="'construction-element-net-area-input'" />
                                <td ng-include="'construction-element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- INTERMEDIATE FLOOR -->
            <div ng-repeat="parent in vm.surfacesInCategory('IntermediateFloor') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'IntermediateFloor' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                        style="min-width: 180px;"
                                        class="clickable">
                                        Adjacent Zone
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Gross Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-click="vm.sortBy('netArea', parent);"
                                        class="clickable">
                                        Net Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-click="vm.sortBy('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'construction-element-gross-area-input'" />
                                    <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                        ng-include="'construction-element-net-area-input'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- INTERMEDIATE FLOOR (NEIGHBOUR BELOW) -->
            <div ng-repeat="parent in vm.surfacesInCategory('IntermediateFloorNeighbourBelow') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'IntermediateFloorNeighbourBelow' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Insulation</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Gross Area (m<sup>2</sup>)</span>
                        <span class="header">Net Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-gross-area-input'" />
                        <div ng-include="'construction-parent-net-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                            <tr>
                                <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-click="vm.sortBy('elementNumber', parent);"
                                    class="clickable">
                                    Element
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Parent Zone
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                    ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                    style="min-width: 180px;"
                                    class="clickable">
                                    Adjacent Zone
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-click="vm.sortBy('storey', parent);"
                                    class="clickable"
                                    style="min-width: 180px;">
                                    Storey
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-click="vm.sortBy('grossArea', parent);"
                                    class="clickable">
                                    Gross Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-click="vm.sortBy('netArea', parent);"
                                    class="clickable">
                                    Net Area (m<sup>2</sup>)
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                    <i ng-if="parent.sortInfo.column === 'netArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                </th>
                                <th>
                                    <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                    <div class="clickable"
                                        ng-click="vm.openVisibilityModal(parent)">
                                        <img src="/content/feather/edit.svg" />
                                    </div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                    ng-include="'construction-element-id-input'" />
                                <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                    ng-include="'construction-element-parent-zone-input'" />
                                <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);">
                                    Neighbour
                                </td>
                                <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                    ng-include="'construction-element-storey-calc'" />
                                <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                    ng-include="'construction-element-gross-area-input'" />
                                <td ng-if="vm.isTableColumnVisible('netArea', parent);"
                                    ng-include="'construction-element-net-area-input'" />
                                <td ng-include="'construction-element-action-buttons'" />
                            </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-----------------------------
                OPENINGS TAB
            ------------------------------>

            <!-- EXTERIOR GLAZING -->
            <div ng-repeat="parent in vm.openingsInCategory('ExteriorGlazing') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'ExteriorGlazing' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <table class="construction-parent-header-table divider-both">
                        <thead>
                            <tr>
                                <th class="manufacturer">Manufacturer</th>
                                <th class="opening-style">Opening Style</th>
                                <th class="frame">Frame</th>
                                <th class="solar">Frame Solar Absorptance</th>
                                <th class="glass">Glass</th>
                                <th class="uvalue">U Value</th>
                                <th class="shgc">SHGC</th>
                                <th class="area">Area (m<sup>2</sup>)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="manufacturer" ng-include="'construction-parent-manufacturer-input'" />
                                <td class="opening-style" ng-include="'construction-parent-opening-style-input'"></td>
                                <td class="frame" ng-include="'construction-parent-frame-input'"></td>
                                <td class="solar clickable" ng-include="'construction-parent-frame-solar-input'"
                                    style="min-width: 50px;"
                                    ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'frameSolarAbsorptance', 'frameColour');"></td>
                                <td class="glass" ng-include="'construction-parent-glass-input'"></td>
                                <td class="uvalue" ng-include="'construction-parent-uvalue-input'"></td>
                                <td class="shgc" ng-include="'construction-parent-shgc-input'"></td>
                                <td class="area" ng-include="'construction-parent-gross-area-input'"></td>
                            </tr>
                        </tbody>
                    </table>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>

                                <!-- Grouped Headers -->
                                <tr>
                                    <th class="text-left"
                                    style="border-bottom: none;"
                                        colspan="{{vm.getShadowHeaderStartPosition(parent)}}" />
                                    <th style="text-align: center"
                                        colspan="{{vm.getShadowHeaderWidth(parent)}}">
                                        Shading
                                    </th>
                                </tr>

                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-click="vm.sortBy('azimuth', parent);"
                                        class="clickable">
                                        Azimuth ({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-click="vm.sortBy('sector', parent);"
                                        class="clickable">
                                        Sector
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-click="vm.sortBy('height', parent);"
                                        class="clickable">
                                        Height (m)
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-click="vm.sortBy('width', parent);"
                                        class="clickable">
                                        Width (m)
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-click="vm.sortBy('openability', parent);"
                                        class="clickable">
                                        Openability (%)
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('horizontalShading', parent);"
                                        ng-click="vm.sortHorizontalShading(parent)"
                                        class="clickable">
                                        Horizontal
                                        <i ng-if="parent.sortInfo.column === 'horizontalShading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'horizontalShading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('verticalShading', parent);"
                                        ng-click="vm.sortVerticalShading(parent)"
                                        class="clickable">
                                        Vertical
                                        <i ng-if="parent.sortInfo.column === 'verticalShading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'verticalShading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('leftWingWall.shading', parent);"
                                        ng-click="vm.sortBy('leftWingWall.shading', parent, vm.wallOutcomeTransform);"
                                        class="clickable">
                                        Left Wing
                                        <i ng-if="parent.sortInfo.column === 'leftWingWall.shading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'leftWingWall.shading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('rightWingWall.shading', parent);"
                                        ng-click="vm.sortBy('rightWingWall.shading', parent, vm.wallOutcomeTransform);"
                                        class="clickable">
                                        Right Wing
                                        <i ng-if="parent.sortInfo.column === 'rightWingWall.shading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'rightWingWall.shading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-include="'construction-element-azimuth-input'" />
                                    <td ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-include="'construction-element-sector-input'" />
                                    <td ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-include="'construction-element-height-input'" />
                                    <td ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-include="'construction-element-width-input'" />
                                    <td ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-include="'construction-element-openability-input'"/>
                                    <td ng-if="vm.isTableColumnVisible('horizontalShading', parent);"
                                        ng-include="'construction-element-shade-horizontal-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'horizontalShading')" />
                                    <td ng-if="vm.isTableColumnVisible('verticalShading', parent);"
                                        ng-include="'construction-element-shade-vertical-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'verticalShading')" />
                                    <td ng-if="vm.isTableColumnVisible('leftWingWall.shading', parent);"
                                        ng-include="'construction-element-shade-left-wing-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'leftWingWall')" />
                                    <td ng-if="vm.isTableColumnVisible('rightWingWall.shading', parent);"
                                        ng-include="'construction-element-shade-right-wing-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'rightWingWall')" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'openings-element-gross-area-calculation'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- INTERIOR GLAZING -->
            <div ng-repeat="parent in vm.openingsInCategory('InteriorGlazing') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'InteriorGlazing' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <table class="construction-parent-header-table divider-both">
                        <thead>
                            <tr>
                                <th class="manufacturer">Manufacturer</th>
                                <th class="opening-style">Opening Style</th>
                                <th class="frame">Frame</th>
                                <th class="solar">Frame Solar Absorptance</th>
                                <th class="glass">Glass</th>
                                <th class="uvalue">U Value</th>
                                <th class="shgc">SHGC</th>
                                <th class="area">Area (m<sup>2</sup>)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="manufacturer" ng-include="'construction-parent-manufacturer-input'" />
                                <td class="opening-style" ng-include="'construction-parent-opening-style-input'"></td>
                                <td class="frame" ng-include="'construction-parent-frame-input'"></td>
                                <td class="solar clickable" ng-include="'construction-parent-frame-solar-input'"
                                    style="min-width: 50px;"
                                    ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'frameSolarAbsorptance', 'frameColour');"></td>
                                <td class="glass" ng-include="'construction-parent-glass-input'"></td>
                                <td class="uvalue" ng-include="'construction-parent-uvalue-input'"></td>
                                <td class="shgc" ng-include="'construction-parent-shgc-input'"></td>
                                <td class="area" ng-include="'construction-parent-gross-area-input'"></td>
                            </tr>
                        </tbody>
                    </table>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                        style="min-width: 180px;"
                                        class="clickable">
                                        Adjacent Zone
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-click="vm.sortBy('height', parent);"
                                        class="clickable">
                                        Height (m)
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-click="vm.sortBy('width', parent);"
                                        class="clickable">
                                        Width (m)
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-click="vm.sortBy('openability', parent);"
                                        class="clickable">
                                        Openability (%)
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-include="'construction-element-height-input'" />
                                    <td ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-include="'construction-element-width-input'" />
                                    <td ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-include="'construction-element-openability-input'"/>
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'construction-element-gross-area-input'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- EXTERIOR DOORS -->
            <div ng-repeat="parent in vm.surfacesInCategory('ExteriorDoor') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'ExteriorDoor' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!--Headers-->
                        <span class="header">Manufacturer</span>
                        <span class="header">Insulation</span>
                        <span class="header opening-style">Opening Style</span>
                        <span class="header">Exterior Solar Absorptance</span>
                        <span class="header">Area (m<sup>2</sup>)</span>

                        <!--Data-->
                        <div ng-include="'construction-parent-manufacturer-input'" />
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div class="opening-style" ng-include="'construction-parent-opening-style-input'" />
                        <div ng-include="'construction-parent-exterior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'exteriorSolarAbsorptance', 'exteriorColour');"/>
                        <div ng-include="'construction-parent-gross-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>

                                <!-- Grouped Headers -->
                                <tr>
                                    <th class="text-left"
                                        style="border-bottom: none;"
                                        colspan="{{vm.getShadowHeaderStartPosition(parent)}}" />
                                    <th style="text-align: center"
                                        colspan="{{vm.getShadowHeaderWidth(parent)}}">
                                        Shading
                                    </th>
                                </tr>

                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-click="vm.sortBy('azimuth', parent);"
                                        class="clickable">
                                        Azimuth ({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-click="vm.sortBy('sector', parent);"
                                        class="clickable">
                                        Sector
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-click="vm.sortBy('height', parent);"
                                        class="clickable">
                                        Height (m)
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-click="vm.sortBy('width', parent);"
                                        class="clickable">
                                        Width (m)
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-click="vm.sortBy('openability', parent);"
                                        class="clickable">
                                        Openability (%)
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('horizontalShading', parent);"
                                        ng-click="vm.sortHorizontalShading(parent)"
                                        class="clickable">
                                        Horizontal
                                        <i ng-if="parent.sortInfo.column === 'horizontalShading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'horizontalShading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('verticalShading', parent);"
                                        ng-click="vm.sortVerticalShading(parent)"
                                        class="clickable">
                                        Vertical
                                        <i ng-if="parent.sortInfo.column === 'verticalShading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'verticalShading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('leftWingWall.shading', parent);"
                                        ng-click="vm.sortBy('leftWingWall.shading', parent, vm.wallOutcomeTransform);"
                                        class="clickable">
                                        Left Wing
                                        <i ng-if="parent.sortInfo.column === 'leftWingWall.shading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'leftWingWall.shading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('rightWingWall.shading', parent);"
                                        ng-click="vm.sortBy('rightWingWall.shading', parent, vm.wallOutcomeTransform);"
                                        class="clickable">
                                        Right Wing
                                        <i ng-if="parent.sortInfo.column === 'rightWingWall.shading' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'rightWingWall.shading' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                            <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-include="'construction-element-azimuth-input'" />
                                    <td ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-include="'construction-element-sector-input'" />
                                    <td ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-include="'construction-element-height-input'" />
                                    <td ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-include="'construction-element-width-input'" />
                                    <td ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-include="'construction-element-openability-input'"/>
                                    <td ng-if="vm.isTableColumnVisible('horizontalShading', parent);"
                                        ng-include="'construction-element-shade-horizontal-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'horizontalShading')" />
                                    <td ng-if="vm.isTableColumnVisible('verticalShading', parent);"
                                        ng-include="'construction-element-shade-vertical-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'verticalShading')" />
                                    <td ng-if="vm.isTableColumnVisible('leftWingWall.shading', parent);"
                                        ng-include="'construction-element-shade-left-wing-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'leftWingWall')" />
                                    <td ng-if="vm.isTableColumnVisible('rightWingWall.shading', parent);"
                                        ng-include="'construction-element-shade-right-wing-input-optional'"
                                        ng-click="vm.openShadingModal(item, 'rightWingWall')" />
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'openings-element-gross-area-calculation'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- INTERIOR DOORS -->
            <div ng-repeat="parent in vm.surfacesInCategory('InteriorDoor') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'InteriorDoor' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Manufacturer</span>
                        <span class="header">Insulation</span>
                        <span class="header">Opening Style</span>
                        <span class="header">Interior Solar Absorptance</span>
                        <span class="header">Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-manufacturer-input'" />
                        <div ng-include="'construction-parent-insulation-input'" />
                        <div class="opening-style" ng-include="'construction-parent-opening-style-input'" />
                        <div ng-include="'construction-parent-interior-solar-input'"
                             style="min-width: 50px;"
                             class="clickable"
                             ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'interiorSolarAbsorptance', 'interiorColour');"/>
                        <div ng-include="'construction-parent-gross-area-input'" />
                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                        style="min-width: 180px;"
                                        class="clickable">
                                        Adjacent Zone
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-click="vm.sortBy('height', parent);"
                                        class="clickable">
                                        Height (m)
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-click="vm.sortBy('width', parent);"
                                        class="clickable">
                                        Width (m)
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-click="vm.sortBy('openability', parent);"
                                        class="clickable">
                                        Openability (%)
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                            <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-include="'construction-element-height-input'" />
                                    <td ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-include="'construction-element-width-input'" />
                                    <td ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-include="'construction-element-openability-input'"/>
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'openings-element-gross-area-calculation'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- VERTICAL OPENINGS -->
            <div ng-repeat="parent in vm.surfacesInCategory('VerticalOpening') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'VerticalOpening' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Manufacturer</span>
                        <span class="header">Opening Style</span>
                        <span class="header">Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-manufacturer-input'" />
                        <div class="opening-style-xl" ng-include="'construction-parent-permanent-opening-style-input'" />
                        <div ng-include="'construction-parent-gross-area-input'" />

                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                        style="min-width: 180px;"
                                        class="clickable">
                                        Adjacent Zone
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-click="vm.sortBy('height', parent);"
                                        class="clickable">
                                        Height (m)
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-click="vm.sortBy('width', parent);"
                                        class="clickable">
                                        Width (m)
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-click="vm.sortBy('openability', parent);"
                                        class="clickable">
                                        Openability (%)
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-include="'construction-element-height-input-optional'" />
                                    <td ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-include="'construction-element-width-input-optional'" />
                                    <td ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-include="'construction-element-openability-input'"/>
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'openings-element-gross-area-calculation'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- HORIZONTAL OPENINGS -->
            <div ng-repeat="parent in vm.surfacesInCategory('HorizontalOpening') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'HorizontalOpening' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <div class="divider-both"
                         style="display: grid; grid-template-columns: 1fr 1fr 1fr;
                                justify-content: space-evenly; justify-items: center; align-items: center;">

                        <!-- Headers -->
                        <span class="header">Manufacturer</span>
                        <span class="header">Opening Style</span>
                        <span class="header">Area (m<sup>2</sup>)</span>

                        <!-- Data -->
                        <div ng-include="'construction-parent-manufacturer-input'" />
                        <div class="opening-style-xl" ng-include="'construction-parent-permanent-opening-style-input'" />
                        <div ng-include="'construction-parent-gross-area-input'" />

                    </div>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-click="vm.sortBy('adjacentZoneNumber', parent, vm.transfromZoneNumberToDescription)"
                                        style="min-width: 180px;"
                                        class="clickable">
                                        Adjacent Zone
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'adjacentZoneNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 180px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-click="vm.sortBy('height', parent);"
                                        class="clickable">
                                        Height (m)
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'height' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-click="vm.sortBy('width', parent);"
                                        class="clickable">
                                        Width (m)
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'width' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-click="vm.sortBy('openability', parent);"
                                        class="clickable">
                                        Openability (%)
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable">
                                        Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="more-actions-icon-column">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('adjacentZoneNumber', parent);"
                                        ng-include="'construction-element-adjacent-zone-input'" />
                                    <td ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td ng-if="vm.isTableColumnVisible('height', parent);"
                                        ng-include="'construction-element-height-input'" />
                                    <td ng-if="vm.isTableColumnVisible('width', parent);"
                                        ng-include="'construction-element-width-input'" />
                                    <td ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-include="'construction-element-openability-input'"/>
                                    <td ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'openings-element-gross-area-calculation'" />
                                    <td ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>

                </div>

            </div>

            <!-- SKYLIGHT -->
            <div ng-repeat="parent in vm.openingsInCategory('Skylight') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'Skylight' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%"  ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <table class="construction-parent-header-table divider-both">
                        <thead>
                            <tr>
                                <th class="manufacturer">Manufacturer</th>
                                <th class="opening-style">Opening Style</th>
                                <th class="frame">Frame</th>
                                <th class="solar">Frame Solar Absorptance</th>
                                <th class="glass">Glass</th>
                                <th class="uvalue">U Value</th>
                                <th class="shgc">SHGC</th>
                                <th class="area">Area (m<sup>2</sup>)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="manufacturer" ng-include="'construction-parent-manufacturer-input'" />
                                <td class="opening-style" ng-include="'construction-parent-opening-style-input'"></td>
                                <td class="frame" ng-include="'construction-parent-frame-input'"></td>
                                <td class="solar clickable" ng-include="'construction-parent-frame-solar-input'"
                                    style="min-width: 50px;"
                                    ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'frameSolarAbsorptance', 'frameColour');"></td>
                                <td class="glass" ng-include="'construction-parent-glass-input'"></td>
                                <td class="uvalue" ng-include="'construction-parent-uvalue-input'"></td>
                                <td class="shgc" ng-include="'construction-parent-shgc-input'"></td>
                                <td class="area" ng-include="'construction-parent-gross-area-input'"></td>
                            </tr>
                        </tbody>
                    </table>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="width: 240px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 120px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('hasInteriorShades', parent);"
                                        ng-click="vm.sortBy('hasInteriorShades', parent);"
                                        class="boolean-cell clickable">
                                        Interior<br/>Shading
                                        <i ng-if="parent.sortInfo.column === 'hasInteriorShades' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'hasInteriorShades' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('hasExteriorShades', parent);"
                                        ng-click="vm.sortBy('hasExteriorShades', parent);"
                                        class="boolean-cell clickable">
                                        Exterior<br/>Shading
                                        <i ng-if="parent.sortInfo.column === 'hasExteriorShades' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'hasExteriorShades' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('shaftLength', parent);"
                                        ng-click="vm.sortBy('shaftLength', parent);"
                                        class="numeric-cell small clickable">
                                        Shaft<br/>Length (m)
                                        <i ng-if="parent.sortInfo.column === 'shaftLength' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'shaftLength' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('shaftArea', parent);"
                                        ng-click="vm.sortBy('shaftArea', parent);"
                                        class="numeric-cell small clickable">
                                        Shaft<br/>Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'shaftArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'shaftArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('shaftReflectance', parent);"
                                        ng-click="vm.sortBy('shaftReflectance', parent);"
                                        class="numeric-cell small clickable">
                                        Shaft<br/>Reflectance
                                        <i ng-if="parent.sortInfo.column === 'shaftReflectance' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'shaftReflectance' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('shaftWallRValue', parent);"
                                        ng-click="vm.sortBy('shaftWallRValue', parent);"
                                        class="numeric-cell small clickable">
                                        Shaft<br/>Resistance
                                        <i ng-if="parent.sortInfo.column === 'shaftWallRValue' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'shaftWallRValue' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('hasDiffuser', parent);"
                                        ng-click="vm.sortBy('hasDiffuser', parent);"
                                        class="boolean-cell clickable">
                                        Diffuser
                                        <i ng-if="parent.sortInfo.column === 'hasDiffuser' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'hasDiffuser' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('tilt', parent);"
                                        ng-click="vm.sortBy('tilt', parent);"
                                        class="numeric-cell small clickable">
                                        Pitch<br/>({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-click="vm.sortBy('azimuth', parent);"
                                        class="numeric cell small clickable">
                                        Azimuth<br/>({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-click="vm.sortBy('sector', parent);"
                                        class="clickable">
                                        Sector
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-click="vm.sortBy('openability', parent);"
                                        class="clickable">
                                        Openability<br/>(%)
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable numeric-cell small">
                                        Area<br/>(m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="action-cell">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td class="element"
                                        ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td class="parent"
                                        ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td class="storey"
                                        ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td class="boolean-cell"
                                        ng-if="vm.isTableColumnVisible('hasInteriorShades', parent);"
                                        ng-include="'construction-element-interior-shading-input'" />
                                    <td class="boolean-cell"
                                        ng-if="vm.isTableColumnVisible('hasExteriorShades', parent);"
                                        ng-include="'construction-element-exterior-shading-input'" />
                                    <td class="numeric-cell"
                                        ng-if="vm.isTableColumnVisible('shaftLength', parent);"
                                        ng-include="'construction-element-shaft-length-input'" />
                                    <td class="numeric-cell"
                                        ng-if="vm.isTableColumnVisible('shaftArea', parent);"
                                        ng-include="'construction-element-shaft-area-input'" />
                                    <td class="numeric-cell"
                                        ng-if="vm.isTableColumnVisible('shaftReflectance', parent);"
                                        ng-include="'construction-element-shaft-reflectance-input'" />
                                    <td class="numeric-cell"
                                        ng-if="vm.isTableColumnVisible('shaftWallRValue', parent);"
                                        ng-include="'construction-element-shaft-resistance-input'" />
                                    <td class="boolean-cell"
                                        ng-if="vm.isTableColumnVisible('hasDiffuser', parent);"
                                        ng-include="'construction-element-diffuser-input'" />
                                    <td class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('tilt', parent);"
                                        ng-include="'construction-element-tilt-input'" />
                                    <td class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-include="'construction-element-azimuth-input'" />
                                    <td class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-include="'construction-element-sector-input'" />
                                    <td class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-include="'construction-element-openability-input'"/>
                                    <td class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'construction-element-gross-area-input'" />
                                    <td class="action-cell" ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- ROOF WINDOW -->
            <div ng-repeat="parent in vm.openingsInCategory('RoofWindow') track by parent.constructionId"
                 ng-if="category.constructionCategoryCode == 'RoofWindow' && (
                        category.allowExternalData == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == false ||
                        vm.building.categoriesWithExternalData[category.constructionCategoryCode.toLowerCase()] == null)"
                 class="construction-parent-body"
                 ng-style="{ 'opacity':  !parent.showInReport ? 'var(--hidden-render-opacity)' : '100%' }">

                <!-- PARENT HEADER/TABLE-->
                <div class="construction-parent-header">

                    <!-- Header on Left-->
                    <div style="width: 99%" ng-include="'construction-parent-description-input'"  />

                    <!-- Central 'Table'-->
                    <table class="construction-parent-header-table divider-both">
                        <thead>
                            <tr>
                                <th class="manufacturer">Manufacturer</th>
                                <th class="opening-style">Opening Style</th>
                                <th class="frame">Frame</th>
                                <th class="solar">Frame Solar Absorptance</th>
                                <th class="glass">Glass</th>
                                <th class="uvalue">U Value</th>
                                <th class="shgc">SHGC</th>
                                <th class="area">Area (m<sup>2</sup>)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="manufacturer" ng-include="'construction-parent-manufacturer-input'" />
                                <td class="opening-style" ng-include="'construction-parent-opening-style-input'"></td>
                                <td class="frame" ng-include="'construction-parent-frame-input'"></td>
                                <td class="solar clickable" ng-include="'construction-parent-frame-solar-input'"
                                    style="min-width: 50px;"
                                    ng-click="vm.openSolarAbsorptanceOverrideModal(parent, 'frameSolarAbsorptance', 'frameColour');"></td>
                                <td class="glass" ng-include="'construction-parent-glass-input'"></td>
                                <td class="uvalue" ng-include="'construction-parent-uvalue-input'"></td>
                                <td class="shgc" ng-include="'construction-parent-shgc-input'"></td>
                                <td class="area" ng-include="'construction-parent-gross-area-input'"></td>
                            </tr>
                        </tbody>
                    </table>

                </div>

                <!-- COLLAPSABLE BODY -->
                <div ng-if="parent.isExpanded">
                    <!-- ELEMENTS TABLE -->
                    <div class="elements-body">

                        <table class="elements-table">

                            <!-- Element Headers -->
                            <thead>
                                <tr>
                                    <th ng-include="'construction-element-bulk-edit-checkbox-master'" class="bulk-edit-column"/>
                                    <th ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-click="vm.sortBy('elementNumber', parent);"
                                        class="clickable">
                                        Element
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'elementNumber' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-click="vm.sortBy('parentZoneId', parent, vm.transfromZoneIdToDescription)"
                                        class="clickable"
                                        style="width: 240px;">
                                        Parent Zone
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'parentZoneId' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-click="vm.sortBy('storey', parent);"
                                        class="clickable"
                                        style="min-width: 120px;">
                                        Storey
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'storey' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('hasInteriorShades', parent);"
                                        ng-click="vm.sortBy('hasInteriorShades', parent);"
                                        class="boolean-cell clickable">
                                        Interior<br/>Shading
                                        <i ng-if="parent.sortInfo.column === 'hasInteriorShades' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'hasInteriorShades' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('hasExteriorShades', parent);"
                                        ng-click="vm.sortBy('hasExteriorShades', parent);"
                                        class="boolean-cell clickable">
                                        Exterior<br/>Shading
                                        <i ng-if="parent.sortInfo.column === 'hasExteriorShades' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'hasExteriorShades' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('shaftLength', parent);"
                                        ng-click="vm.sortBy('shaftLength', parent);"
                                        class="numeric-cell clickable">
                                        Shaft<br/>Length (m)
                                        <i ng-if="parent.sortInfo.column === 'shaftLength' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'shaftLength' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('shaftArea', parent);"
                                        ng-click="vm.sortBy('shaftArea', parent);"
                                        class="numeric-cell clickable">
                                        Shaft<br/>Area (m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'shaftArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'shaftArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('shaftReflectance', parent);"
                                        ng-click="vm.sortBy('shaftReflectance', parent);"
                                        class="numeric-cell clickable">
                                        Shaft<br/>Reflectance
                                        <i ng-if="parent.sortInfo.column === 'shaftReflectance' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'shaftReflectance' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('shaftWallRValue', parent);"
                                        ng-click="vm.sortBy('shaftWallRValue', parent);"
                                        class="numeric-cell clickable">
                                        Shaft<br/>Resistance
                                        <i ng-if="parent.sortInfo.column === 'shaftWallRValue' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'shaftWallRValue' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('hasDiffuser', parent);"
                                        ng-click="vm.sortBy('hasDiffuser', parent);"
                                        class="boolean-cell clickable">
                                        Diffuser
                                        <i ng-if="parent.sortInfo.column === 'hasDiffuser' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'hasDiffuser' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('tilt', parent);"
                                        ng-click="vm.sortBy('tilt', parent);"
                                        class="numeric-cell small clickable">
                                        Pitch<br/>({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'tilt' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-click="vm.sortBy('azimuth', parent);"
                                        class="numeric cell small clickable">
                                        Azimuth<br/>({{vm.symbol("degrees")}})
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'azimuth' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-click="vm.sortBy('sector', parent);"
                                        class="clickable">
                                        Sector
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'sector' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-click="vm.sortBy('openability', parent);"
                                        class="clickable">
                                        Openability<br/>(%)
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'openability' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-click="vm.sortBy('grossArea', parent);"
                                        class="clickable numeric-cell small">
                                        Area<br/>(m<sup>2</sup>)
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'ASC'" class="fa fa-caret-up" />
                                        <i ng-if="parent.sortInfo.column === 'grossArea' && parent.sortInfo.direction === 'DESC'" class="fa fa-caret-down" />
                                    </th>
                                    <th class="action-cell">
                                        <!-- Edit column icon (Fake button so parent <fieldset> never disables it) -->
                                        <div class="clickable"
                                            ng-click="vm.openVisibilityModal(parent)">
                                            <img src="/content/feather/edit.svg" />
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.elementsInOrder(parent) track by item.constructionId">
                                    <td ng-include="'construction-element-bulk-edit-checkbox'"/>
                                    <td
                                        class="element"
                                        ng-if="vm.isTableColumnVisible('elementNumber', parent);"
                                        ng-include="'construction-element-id-input'" />
                                    <td
                                        class="parent"
                                        ng-if="vm.isTableColumnVisible('parentZoneId', parent);"
                                        ng-include="'construction-element-parent-zone-input'" />
                                    <td
                                        class="storey"
                                        ng-if="vm.isTableColumnVisible('storey', parent);"
                                        ng-include="'construction-element-storey-calc'" />
                                    <td
                                        class="boolean-cell"
                                        ng-if="vm.isTableColumnVisible('hasInteriorShades', parent);"
                                        ng-include="'construction-element-interior-shading-input'" />
                                    <td
                                        class="boolean-cell"
                                        ng-if="vm.isTableColumnVisible('hasExteriorShades', parent);"
                                        ng-include="'construction-element-exterior-shading-input'" />
                                    <td
                                        class="numeric-cell"
                                        ng-if="vm.isTableColumnVisible('shaftLength', parent);"
                                        ng-include="'construction-element-shaft-length-input'" />
                                    <td
                                        class="numeric-cell"
                                        ng-if="vm.isTableColumnVisible('shaftArea', parent);"
                                        ng-include="'construction-element-shaft-area-input'" />
                                    <td
                                        class="numeric-cell"
                                        ng-if="vm.isTableColumnVisible('shaftReflectance', parent);"
                                        ng-include="'construction-element-shaft-reflectance-input'" />
                                    <td
                                        class="numeric-cell"
                                        ng-if="vm.isTableColumnVisible('shaftWallRValue', parent);"
                                        ng-include="'construction-element-shaft-resistance-input'" />
                                    <td
                                        class="boolean-cell"
                                        ng-if="vm.isTableColumnVisible('hasDiffuser', parent);"
                                        ng-include="'construction-element-diffuser-input'" />

                                    <td
                                        class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('tilt', parent);"
                                        ng-include="'construction-element-tilt-input'" />
                                    <td
                                        class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('azimuth', parent);"
                                        ng-include="'construction-element-azimuth-input'" />
                                    <td
                                        class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('sector', parent);"
                                        ng-include="'construction-element-sector-input'" />
                                    <td
                                        class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('openability', parent);"
                                        ng-include="'construction-element-openability-input'"/>
                                    <td
                                        class="numeric-cell small"
                                        ng-if="vm.isTableColumnVisible('grossArea', parent);"
                                        ng-include="'construction-element-gross-area-input'" />
                                    <td
                                        class="action-cell"
                                        ng-include="'construction-element-action-buttons'" />
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <div ng-include="'construction-parent-lower-action-buttons'"></div>
                </div>

            </div>

            <!-- ADD NEW CONSTRUCTIONS -->
            <div ng-include="'construction-template-search'"></div>

        </md-card-content>
    </md-card>

</div>

<!-- Group Action Buttons -->
<script type="text/ng-template" id="construction-parent-action-buttons">
    <div style="display: flex; justify-content: center;">

        <!-- 'More' button w/ Popup -->
        <md-menu ng-show="!vm.disabled">

            <!-- Initial '...' button, which launches options -->
            <img md-menu-origin
                 class="clickable"
                 ng-click="$mdOpenMenu()"
                 src="/content/feather/more-horizontal.svg"
                 ng-disabled="vm.disabled"/>
            <md-menu-content>

                <!-- Rename parent item -->
                <md-menu-item>
                    <md-button ng-click="vm.launchParentActionModal('rename', parent, building)"
                               ng-disabled="vm.disabledEx()">
                        Rename
                    </md-button>
                </md-menu-item>

                <!-- Substitube parent item -->
                <md-menu-item>
                    <md-button ng-click="vm.launchParentActionModal('substitute', parent, building)"
                               ng-disabled="vm.disabledEx()">
                        Substitute
                    </md-button>
                </md-menu-item>

                <!-- Duplicate Parent Button -->
                <md-menu-item>
                    <md-button ng-click="vm.duplicateParent(parent, building)"
                               ng-disabled="vm.disabledEx()">
                        Duplicate
                    </md-button>
                </md-menu-item>

                <!-- Show / Hide Button -->
                <md-menu-item>
                    <md-button ng-disabled="vm.disabledEx()"
                               ng-click="parent.showInReport = !parent.showInReport">
                        <span ng-show="parent.showInReport">Hide</span>
                        <span ng-show="!parent.showInReport">Show</span>
                    </md-button>
                </md-menu-item>

                <md-menu-divider></md-menu-divider>

                <!-- Delete Parent Button -->
                <md-menu-item>
                    <md-button ng-click="vm.removeConstructionParent(parent, building)"
                               ng-disabled="vm.disabledEx()">
                        <span style="color: orangered;">Delete</span>
                    </md-button>
                </md-menu-item>

            </md-menu-content>
        </md-menu>
    </div>
</script>

<!-- Element Action Buttons -->
<script type="text/ng-template" id="construction-element-action-buttons">
    <div style="display: flex; justify-content: center;">

        <!-- 'More' button w/ Popup -->
        <md-menu ng-show="!vm.disabled">

            <!-- Initial '...' button, which launches options -->
            <img md-menu-origin
                 class="clickable"
                 ng-click="$mdOpenMenu()"
                 src="/content/feather/more-horizontal.svg"
                 ng-disabled="vm.disabled"/>
            <md-menu-content>

                <!-- Duplicate Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.copyElement(parent, item)"
                               ng-disabled="vm.disabledEx()">
                        Duplicate
                    </md-button>
                </md-menu-item>

                <!-- Move or copy Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.launchElementActionModal(parent, building, [item])"
                               ng-disabled="vm.disabledEx()">
                        Move or copy
                    </md-button>
                </md-menu-item>

                <md-menu-divider></md-menu-divider>

                <!-- Delete Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.deleteElement(parent, item)"
                               ng-disabled="vm.disabledEx()">
                        <span style="color: orangered;">Delete</span>
                    </md-button>
                </md-menu-item>

            </md-menu-content>
        </md-menu>

    </div>
</script>

<!-- Expand / Collapse Parent button -->
<script type="text/ng-template" id="expand-collapse-button">
    <span style="margin: 0 10px; display: inline-block;"
          ng-click="parent.isExpanded = !parent.isExpanded;">
        <i ng-if="parent.isExpanded"
           class="fa fa-caret-up" style="font-size: 18px;" />
        <i ng-if="(!parent.isExpanded)"
           class="fa fa-caret-down" style="font-size: 18px;" />
    </span>
</script>

<!-- Add new Construction selector. -->
<script type="text/ng-template" id="construction-template-search">

    <!--
        Switch between showing an ADD BUTTON and an autocomplete.
        De-selecting the auto-complete should result in the Add button appearing again
        and vice-versa.
    -->
    <md-button ng-show="!vm.disabledEx() && (category.inAddMode == null || category.inAddMode == false)"
               class="md-primary"
               ng-click="vm.switchToAddMode(category);"
               ng-disabled="vm.disabledEx()">
        {{category.type.toLowerCase() == 'surface' &&
          category.constructionCategoryCode !== 'ExteriorDoor' &&
          category.constructionCategoryCode !== 'InteriorDoor' ? 'ADD CONSTRUCTION' : 'ADD OPENING'}}
    </md-button>
    <div ng-if="!vm.disabledEx() && category.inAddMode == true"
         layout="row">
        <md-autocomplete flex="95"
                         id="Template{{category.constructionCategoryCode}}"
                         md-input-name="template"
                         md-selected-item="tempTemplate"
                         md-auto-focus=""
                         md-selected-item-change="vm.addConstructionParentFromTemplate(category, building, tempTemplate)"
                         md-search-text="templateSearchText"
                         md-items="listItem in vm.filteredConstructionTemplateList({{category}}) | filter: { description: templateSearchText }"
                         md-item-text="listItem.description"
                         md-min-length="0"
                         md-select-on-match="true"
                         md-require-match="true"
                         placeholder="Search..."
                         class="glowing vertically-condensed kindly-remove-error-space"
                         style="margin: 10px">
            <md-item-template>
                <span md-highlight-text="templateSearchText"
                      md-highlight-flags="^i">{{listItem.description}}</span>
            </md-item-template>
        </md-autocomplete>

        <md-button class="md-icon-button checkbox-aligner"
                   flex="5"
                   ng-click="category.inAddMode = false;"
                   style="margin: 10px">
            <i class="material-icons">
                clear
            </i>
        </md-button>
    </div>

</script>

<script type="text/ng-template" id="construction-parent-description-input">
    <div style="display: grid; grid-template-columns: 30px 1fr 40px; align-items: center;">

        <!-- Expand/Close Button -->
        <span class="clickable"
              style="margin: 0 10px; display: inline-block;"
              ng-click="parent.isExpanded = !parent.isExpanded">
            <i ng-show="parent.isExpanded"
               class="fa fa-caret-up" style="font-size: 18px;" />
            <i ng-show="(!parent.isExpanded)"
               class="fa fa-caret-down" style="font-size: 18px;" />
        </span>

        <!-- Construction Description -->
        <span class="lightweight construction-parent-title"
              disabled>

            <!-- Non-clickable description / title -->
            <span>
                {{parent.overrideDisplayDescription || parent.displayDescription || parent.description}}
            </span>
        </span>

        <!-- Parent Actions -->
        <span ng-include="'construction-parent-action-buttons'"></span>

    </div>
</script>

<script type="text/ng-template" id="construction-parent-gross-area-calculation">
    <input class="lightweight"
           disabled
           ng-value="parent.grossArea.toFixed(2)" />
</script>

<script type="text/ng-template" id="construction-parent-width-calculation">
    <input class="lightweight"
           disabled
           ng-value="parent.width.toFixed(2)" />
</script>

<script type="text/ng-template" id="construction-parent-gross-area-input">
    <input class="lightweight"
           disabled
           ng-value="parent.grossArea.toFixed(2)" />
</script>

<script type="text/ng-template" id="construction-parent-net-area-input">
    <input class="lightweight"
           disabled
           ng-value="parent.netArea.toFixed(2)" />
</script>

<!-- Roof Space Ventilation -->
<script type="text/ng-template" id="construction-parent-ventilation-input">
    <md-select required
               style="margin: auto;"
               name="airCavity"
               ng-disabled="vm.disabled || item.source === 'external'"
               ng-model="parent.airCavity"
               ng-model-options="{trackBy: '$value.airCavityCode'}">
        <md-option ng-value="null"></md-option>
        <md-option ng-value="ac"
                   ng-repeat="ac in vm.airCavityList | filter:subfloorVentilationFilter(category)">
            {{ac.title}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="construction-parent-interior-solar-input">
    <div id="{{parent.constructionId}}InteriorSolarInput"
           class="lightweight" style="color: grey;">
        {{parent.interiorSolarAbsorptance.toFixed(2)}} &nbsp;
    </div>
</script>

<script type="text/ng-template" id="construction-parent-frame-solar-input">
    <div id="{{parent.constructionId}}FrameSolarInput"
          class="lightweight" style="color: grey;">
        {{parent.frameSolarAbsorptance.toFixed(2)}} &nbsp;
    </div>
</script>

<script type="text/ng-template" id="construction-parent-exterior-solar-input">
    <div id="{{parent.constructionId}}ExteriorSolarInput"
          class="lightweight" style="color: grey;">
        {{parent.exteriorSolarAbsorptance.toFixed(2)}} &nbsp;
    </div>
</script>

<script type="text/ng-template" id="construction-parent-insulation-input">
    <div>

        <!-- Use override insulation description if it exists, otherwise fall back to original -->
        <span ng-if="parent.overrideInsulationDescription || (parent.insulationData != null && parent.insulationData.description != null)"
              class="lightweight disabled">
            {{parent.overrideInsulationDescription || parent.insulationData.description}}
        </span>

        <span ng-if="!parent.overrideInsulationDescription && (parent.insulationData == null || parent.insulationData.description == null)"
              class="lightweight disabled"
              style="white-space: nowrap;">
            No Insulation
        </span>

    </div>
</script>

<script type="text/ng-template" id="construction-element-bulk-edit-checkbox-master">
    <md-checkbox id="{{parent.constructionId}}allCheckbox"
                 ng-disabled="vm.disabledEx()"
                 ng-model="parent.selectAllCheckboxState"
                 md-indeterminate="parent.bulkSelectCheckboxIsIndeterminate || false"
                 ng-click="vm.selectAllElementCheckboxes(parent, parent.selectAllCheckboxState)"
                 style="width: 20px; margin: 0; padding: 0;">
    </md-checkbox>
</script>

<script type="text/ng-template" id="construction-element-bulk-edit-checkbox">
    <md-checkbox ng-model="item.checkboxSelected"
                 ng-disabled="vm.disabledEx()"
                 ng-change="vm.updateBulkSelectStatus(parent);"
                 style="width: 20px; margin: 0; padding: 0;;">
    </md-checkbox>
</script>

<script type="text/ng-template" id="construction-element-id-input">
    <input disabled
           ng-required="true"
           ng-model="item.elementNumber"
           style="width: 60px;" />
</script>

<script type="text/ng-template" id="construction-element-parent-zone-input">
    <md-select ng-required="true"
               name="ParentZone{{$index}}"
               style="margin: 0;"
               ng-disabled="vm.disabled || item.source === 'external'"
               ng-model="item.parentZoneId">
        <md-option ng-value="x.linkId"
                   ng-click="vm.elementZoneChanged(item, x)"
                   ng-repeat="x in vm.availableParentZonesForCategory(parent.category)">
            {{x.zoneDescription}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="construction-element-parent-zone-subfloor-only-input">
    <md-select ng-required="true"
               name="ParentZone{{$index}}"
               style="margin: 0;"
               ng-disabled="vm.disabled || item.source === 'external'"
               ng-model="item.parentZoneId">
        <md-option ng-value="x.linkId"
                   ng-repeat="x in vm.subfloorZones()">
            {{x.zoneDescription}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="construction-element-storey-calc">
    <input class="lightweight"
           style="width: auto;"
           disabled
           ng-value="vm.floorOfZone(item.parentZoneId)"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-tilt-input">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.tilt"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-tilt-input-optional">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.tilt"/>
</script>

<script type="text/ng-template" id="construction-element-azimuth-input">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.azimuth"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-sector-input">
    <input class="lightweight"
           name="units"
           disabled
           ng-model="item.sector" />
</script>

<!-- This is like gross area INPUT but for when the value is actually the elements width * height. -->
<script type="text/ng-template" id="construction-element-gross-area-calculation">
    <input class="lightweight"
           name="units"
           disabled
           ng-value="item.grossArea.toFixed(2)"
           ng-required="true"/>
</script>
<!-- Same input but triggers modal for area to be overriden for the Openings tab
    (if doesn't already have area logic ie. Skylight, Roof Window and Interior Glazing) -->
<script type="text/ng-template" id="openings-element-gross-area-calculation">
    <input class="lightweight"
            ng-class="{
                'overridable' : item.source === 'manual',
                'overridden' : item.grossAreaIsManuallyOverridden
            }"
            ng-click="vm.openingsOverrideModal(item, 'grossArea')"
            ng-disabled="item.source !== 'manual'"
            name="units"
            ng-value="item.grossArea.toFixed(2)"
            ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-gross-area-input">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.grossArea"
           ng-blur="vm.elementGrossAreaChanged(item)"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-gross-area-input-fixed">
    <input class="lightweight"
           name="units"
           disabled
           ng-value="item.grossArea.toFixed(2)"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-net-area-input">
    <input id="{{item.constructionId}}NetAreaInput"
           formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.netArea"
           ng-blur="vm.elementNetAreaChanged(item)"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-adjacent-zone-input">
    <md-select ng-required="true"
               name="AdjacentZone{{$index}}"
               style="margin: 0;"
               ng-disabled="vm.disabledEx() || item.source === 'external'"
               ng-model="item.adjacentZoneNumber">
        <md-option ng-value="x.zoneNumber"
                   ng-repeat="x in vm.availableAdjacentZonesForCategory(parent.category)">
            {{x.zoneDescription}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="construction-element-height-input">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.height"
           ng-blur="vm.elementDimensionChanged(item)"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-width-input">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.width"
           ng-blur="vm.elementDimensionChanged(item)"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-height-input-optional">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.height"
           ng-blur="vm.elementDimensionChanged(item)"
           ng-required="false" />
</script>

<script type="text/ng-template" id="construction-element-width-input-optional">
    <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.width"
           ng-blur="vm.elementDimensionChanged(item)"
           ng-required="false" />
</script>

<!-- Modal/edit logic should only effect Openings tab -->
<script type="text/ng-template" id="construction-element-openability-input">
    <!-- Enable for source === 'manual' -->
    <input class="lightweight"
        name="openability"
        ng-class="{
            'overridable' : item.source === 'manual',
            'overridden' : item.openabilityIsManuallyOverridden
        }"
        ng-click="vm.openingsOverrideModal(item, 'openability')"
        ng-value="item.openability.toFixed(2)"
        ng-disabled="item.source === 'external'"
        ng-required="true"/>
</script>

<!-- THR-219 | Removed as "shading data is NOT captured for Subfloor Walls in the scratch file"
    <script type="text/ng-template" id="construction-element-shade-projection-input-optional">
        <input formatted-number
            class="lightweight"
            decimals="2"
            name="units"
            ng-disabled="vm.disabledEx() || item.source === 'external'"
            ng-model="item.shadeProjection"
            ng-required="false" />
    </script>
-->

<!-- Four new shading fields replacing the existing two (Shade Projection and Shade Offset)
     For: Exterior Walls (Construction tab), Exterior Glazing and Exterior Doors (Openings tab)
-->
<script type="text/ng-template" id="construction-element-shade-horizontal-input-optional">
    <input class="lightweight table-clickable"
        type="text"
        disabled
        ng-value="vm.greaterShading(item.horizontalProjection1.shading, item.horizontalProjection2.shading)" />
</script>
<script type="text/ng-template" id="construction-element-shade-vertical-input-optional">
    <input class="lightweight table-clickable"
        type="text"
        disabled
        ng-value="vm.greaterShading(item.verticalScreen1.shading, item.verticalScreen2.shading, item.verticalScreen3.shading)" />
</script>
<script type="text/ng-template" id="construction-element-shade-left-wing-input-optional">
    <input class="lightweight table-clickable"
        type="text"
        disabled
        ng-value="vm.elementWingShading(item.leftWingWall)" />
</script>
<script type="text/ng-template" id="construction-element-shade-right-wing-input-optional">
    <input class="lightweight table-clickable"
        type="text"
        disabled
        ng-value="vm.elementWingShading(item.rightWingWall)" />
</script>

<!-- THR-219 | Removed as "shading data is NOT captured for Subfloor Walls in the scratch file"
    <script type="text/ng-template" id="construction-element-shade-offset-input-optional">
        <input formatted-number
           class="lightweight"
           decimals="2"
           name="units"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.shadeOffset"
           ng-required="false" />
    </script>
-->

<!-- OPENING SCRIPTS -->
<script type="text/ng-template" id="construction-parent-manufacturer-input">
    <input class="lightweight disabled"
           type="text"
           disabled
           ng-model="parent.manufacturer.description" />
</script>

<script type="text/ng-template" id="construction-parent-frame-input">
    <md-select ng-required="true"
               style="margin: auto;"
               class="lightweight"
               ng-disabled="vm.disabledEx()"
               ng-model="parent.frameMaterial"
               ng-model-options="{trackBy: '$value.frameMaterialCode'}">

        <md-option ng-value="frameMaterial"
                   ng-repeat="frameMaterial in vm.frameMaterialList">
            {{frameMaterial.title}}
        </md-option>
    </md-select>
</script>

<script type="text/ng-template" id="construction-parent-opening-style-input">

    <!-- TODO: Disable if the construction template had a value ugghh.h -->
    <md-select ng-required="true"
               class="lightweight"
               style="margin: auto;"
               ng-disabled="vm.disabledEx() || parent.allowEditingOpeningStyle == false || item.source === 'external'"
               ng-model="parent.openingStyle"
               ng-model-options="{trackBy: '$value.openingStyleCode'}">

        <!-- fudging sake -->
        <md-option ng-value="style"
                   ng-repeat="style in vm.openingListForCategory(category)"
                   ng-click="vm.updateOpeningStyle(parent, style)">
            {{style.title}}
        </md-option>
    </md-select>

</script>

<script type="text/ng-template" id="construction-parent-permanent-opening-style-input">

    <md-select ng-required="true"
               class="lightweight"
               style="margin: auto;"
               ng-disabled="vm.disabledEx() || parent.allowEditingOpeningStyle == false || item.source === 'external'"
               ng-model="parent.openingStyle"
               ng-model-options="{trackBy: '$value.openingStyleCode'}">

        <md-option ng-value="style"
                   ng-repeat="style in vm.openingStyleList"
                   ng-click="parent.openability = style.defaultOpenability">
            {{style.title}}
        </md-option>
    </md-select>

</script>

<script type="text/ng-template" id="construction-parent-glass-input">
    <input class="lightweight"
           ng-disabled="true"
           ng-model="parent.glassData.description"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-parent-uvalue-input">
    <input id="{{parent.constructionId}}UValueInput"
           class="lightweight"
           formatted-number
           decimals="2"
           name="uValueInput"
           ng-disabled="true"
           ng-model="parent.performance.uValue"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-parent-shgc-input">
    <input id="{{parent.constructionId}}SHGCInput"
           class="lightweight"
           formatted-number
           decimals="2"
           name="units"
           disabled
           ng-model="parent.performance.shgc"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-interior-shading-input">

    <md-select ng-model="item.hasInteriorShades"
               style="margin: 0;"
               ng-required="true"
               ng-disabled="vm.disabledEx() || item.source === 'external'">
        <md-option ng-value="false">No</md-option>
        <md-option ng-value="true">Yes</md-option>
    </md-select>
</script>

<script type="text/ng-template" id="construction-element-exterior-shading-input">
    <md-select ng-model="item.hasExteriorShades"
               style="margin: 0;"
               ng-required="true"
               ng-disabled="vm.disabledEx() || item.source === 'external'">
        <md-option ng-value="false">No</md-option>
        <md-option ng-value="true">Yes</md-option>
    </md-select>
</script>

<script type="text/ng-template" id="construction-element-shaft-length-input">
    <input class="lightweight"
           formatted-number
           decimals="2"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.shaftLength"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-shaft-area-input">
    <input class="lightweight"
           formatted-number
           decimals="2"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.shaftArea"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-shaft-reflectance-input">
    <input class="lightweight"
           formatted-number
           decimals="2"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.shaftReflectance"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-shaft-resistance-input">
    <input class="lightweight"
           formatted-number
           decimals="2"
           ng-disabled="vm.disabledEx() || item.source === 'external'"
           ng-model="item.shaftWallRValue"
           ng-required="true"/>
</script>

<script type="text/ng-template" id="construction-element-diffuser-input">
    <md-select ng-model="item.hasDiffuser"
               style="margin: 0;"
               ng-required="true"
               ng-disabled="vm.disabledEx() || item.source === 'external'">
        <md-option ng-value="false">No</md-option>
        <md-option ng-value="true">Yes</md-option>
    </md-select>
</script>

<script type="text/ng-template" id="construction-parent-lower-action-buttons">

    <div style="display: flex; justify-content: space-between; margin: 10px 0;">

        <!-- 'More' button w/ Popup -->
        <md-menu ng-show="!vm.disabled">

            <!-- Initial bulk edit button, which launches options -->
            <md-button class="md-raised md-primary"
                       ng-click="$mdOpenMenu()"
                       ng-disabled="vm.disabledEx() || !(parent.selectAllCheckboxState || parent.bulkSelectCheckboxIsIndeterminate)">
                BULK EDIT
            </md-button>

            <md-menu-content>

                <!-- Duplicate Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.bulkCopyElements(parent)"
                               ng-disabled="vm.disabledEx()">
                        Duplicate
                    </md-button>
                </md-menu-item>

                <!-- Move or copy Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.launchElementActionModal(parent, building)"
                               ng-disabled="vm.disabledEx()">
                        Move or copy
                    </md-button>
                </md-menu-item>

                <md-menu-divider></md-menu-divider>

                <!-- Delete Element Button -->
                <md-menu-item>
                    <md-button ng-click="vm.bulkDeleteElements(parent)"
                               ng-disabled="vm.disabledEx()">
                        <span style="color: orangered;">Delete</span>
                    </md-button>
                </md-menu-item>

            </md-menu-content>
        </md-menu>

        <add-element-button parent="parent"
                            building="building"
                            disabled="vm.disabledEx()"
                            elements-added-callback="vm.setElementCodesForCategory(categoryCode); vm.sortElements(parent);"/>

    </div>
</script>
